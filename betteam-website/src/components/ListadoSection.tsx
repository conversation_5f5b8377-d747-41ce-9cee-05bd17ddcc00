'use client';

import React from 'react';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import ActionButtons from './ActionButtons';
import { StaggeredPlatformGrid } from './StaggeredGrid';

// 平台卡片数据接口
interface PlatformCard {
  id: number;
  username: string;
  platform: 'Telegram' | 'WhatsApp';
  emoji: string;
  gradientFrom: string;
  gradientTo: string;
  telegramLink?: string; // Telegram 链接
  whatsappLink?: string; // WhatsApp 链接
  tinyUrl?: string; // TinyURL 链接
}

export default function ListadoSection() {
  // 使用多语言 hook
  const t = useTranslations('listado');
  
  // 从多语言配置中获取平台数据数组
  const platformCards: PlatformCard[] = t.raw('platforms') as PlatformCard[];

  return (
    <section 
      id="listado" 
      className="pt-8 pb-8 bg-primary"
      aria-labelledby="listado-title"
    >
      <div className="container mx-auto px-4">
        {/* 章节标题 */}
        <h2 
          id="listado-title"
          className="text-white mb-16"
          style={{
            fontFamily: 'Montserrat',
            fontWeight: 900,
            fontStyle: 'normal',
            fontSize: '30px',
            lineHeight: '100%',
            letterSpacing: '0%',
            textAlign: 'left'
          }}
        >
          {t('title')}
        </h2>

        {/* 平台卡片网格 */}
        <StaggeredPlatformGrid
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {platformCards.map((card) => (
            <article 
              key={card.id} 
              className="p-6 hover:bg-gray-800 transition-all duration-300"
              style={{
                background: '#0051FF1A',
                borderRadius: '24px'
              }}
              role="listitem"
              aria-labelledby={`platform-${card.id}-name`}
            >
              {/* 头部信息区域 */}
              <header className="flex items-start mb-6">
                {/* 头像 - 圆形设计 */}
                <div
                  className="w-14 h-14 rounded-full flex items-center justify-center mr-4 flex-shrink-0 overflow-hidden bg-white"
                  role="img"
                  aria-label={`${card.username} avatar`}
                >
                  <Image
                    src={`/list/${card.username.toUpperCase()}.png`}
                    alt={`${card.username} logo`}
                    width={56}
                    height={56}
                    className="w-full h-full object-cover rounded-full"
                    onError={(e) => {
                      // 如果找不到对应名称的图片，使用默认图片
                      const target = e.target as HTMLImageElement;
                      target.src = '/list/default.png';
                    }}
                  />
                </div>
                
                {/* 用户信息 */}
                <div className="flex-1 min-w-0">
                  <h3 
                    id={`platform-${card.id}-name`}
                    className="text-white mb-1"
                    style={{
                      fontFamily: 'Montserrat',
                      fontWeight: 700,
                      fontSize: '24px',
                      lineHeight: '100%',
                      letterSpacing: '0%'
                    }}
                  >
                    {card.username}
                  </h3>
                  <p className="text-gray-400 text-sm mb-2">{t('officialChannel')}</p>
                  <div className="inline-flex">
                    <span 
                      className="text-white text-xs px-3 py-1 font-medium"
                      style={{
                        background: '#1A27DB66',
                        borderRadius: '4px'
                      }}
                      role="badge"
                      aria-label={`${card.platform}`}
                    >
                      {card.platform}
                    </span>
                  </div>
                </div>
              </header>

              {/* 行动按钮 */}
              <ActionButtons
                playNowText={t('playNow')}
                telegramText={t('telegram')}
                whatsappText={t('whatsapp')}
                tinyUrl={card.tinyUrl}
                telegramLink={card.telegramLink}
                whatsappLink={card.whatsappLink}
              />
            </article>
          ))}
        </StaggeredPlatformGrid>
      </div>
    </section>
  );
}