'use client';

import React from 'react';
import { useTranslations } from 'next-intl';
import ActionButtons from './ActionButtons';
import { StaggeredPlatformGrid } from './StaggeredGrid';

// 平台卡片数据接口
interface PlatformCard {
  id: number;
  username: string;
  platform: 'Telegram' | 'WhatsApp';
  emoji: string;
  gradientFrom: string;
  gradientTo: string;
  telegramLink?: string; // Telegram 链接
  whatsappLink?: string; // WhatsApp 链接
  tinyUrl?: string; // TinyURL 链接
}

export default function ListadoSection() {
  // 使用多语言 hook
  const t = useTranslations('listado');
  
  // 从多语言配置中获取平台数据数组
  const platformCards: PlatformCard[] = t.raw('platforms') as PlatformCard[];

  return (
    <section 
      id="listado" 
      className="pt-8 pb-8 bg-primary"
      aria-labelledby="listado-title"
    >
      <div className="container mx-auto px-4">
        {/* 章节标题 */}
        <h2 
          id="listado-title"
          className="text-white mb-16"
          style={{
            fontFamily: 'Montserrat',
            fontWeight: 900,
            fontStyle: 'normal',
            fontSize: '30px',
            lineHeight: '100%',
            letterSpacing: '0%',
            textAlign: 'left'
          }}
        >
          {t('title')}
        </h2>

        {/* 平台卡片网格 */}
        <StaggeredPlatformGrid
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {platformCards.map((card) => (
            <article 
              key={card.id} 
              className="p-6 hover:bg-gray-800 transition-all duration-300"
              style={{
                background: '#0051FF1A',
                borderRadius: '24px'
              }}
              role="listitem"
              aria-labelledby={`platform-${card.id}-name`}
            >
              {/* 头部信息区域 */}
              <header className="flex items-start mb-6">
                {/* 头像 - 圆形设计 */}
                <div
                  className="flex items-center justify-center mr-4 flex-shrink-0 overflow-hidden"
                  role="img"
                  aria-label={`${card.username} avatar`}
                  style={{
                    width: '100px',
                    height: '100px',
                    borderRadius: '14px',
                    background: 'linear-gradient(135.7deg, #410D0D 3.71%, #1D0404 101.11%)',
                    opacity: 1
                  }}
                >
                  <div
                    className="w-full h-full"
                    style={{
                      borderRadius: '14px',
                      backgroundImage: `url(/list/default.png)`,
                      backgroundSize: 'auto 100%', // 保持图片高度填满，宽度自适应
                      backgroundPosition: 'left center', // 显示图片左侧部分
                      backgroundRepeat: 'no-repeat'
                    }}
                  />
                </div>
                
                {/* 用户信息 */}
                <div className="flex-1 min-w-0">
                  <h3 
                    id={`platform-${card.id}-name`}
                    className="text-white mb-1"
                    style={{
                      fontFamily: 'Montserrat',
                      fontWeight: 700,
                      fontSize: '24px',
                      lineHeight: '100%',
                      letterSpacing: '0%'
                    }}
                  >
                    {card.username}
                  </h3>
                  <p className="text-gray-400 text-sm mb-2">{t('officialChannel')}</p>
                  <div className="inline-flex">
                    <span 
                      className="text-white text-xs px-3 py-1 font-medium"
                      style={{
                        background: '#1A27DB66',
                        borderRadius: '4px'
                      }}
                      role="badge"
                      aria-label={`${card.platform}`}
                    >
                      {card.platform}
                    </span>
                  </div>
                </div>
              </header>

              {/* 行动按钮 */}
              <ActionButtons
                playNowText={t('playNow')}
                telegramText={t('telegram')}
                whatsappText={t('whatsapp')}
                tinyUrl={card.tinyUrl}
                telegramLink={card.telegramLink}
                whatsappLink={card.whatsappLink}
              />
            </article>
          ))}
        </StaggeredPlatformGrid>
      </div>
    </section>
  );
}